package com.tool.converge.business.rules.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tool.converge.business.rules.WarnConditionService;
import com.tool.converge.repository.domain.rules.bo.RulesSaveBO;
import com.tool.converge.repository.domain.rules.bo.RulesUpdateBO;
import com.tool.converge.repository.domain.rules.bo.RulesQueryParamsBO;
import com.tool.converge.repository.domain.rules.bo.WarnConditionUpdateBO;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import com.tool.converge.repository.domain.rules.vo.RulesDetailVO;
import com.tool.converge.repository.domain.rules.vo.RulesPageVO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.mapper.rules.RulesMapper;
import com.tool.converge.business.rules.RulesService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
@Slf4j
@Service
public class RulesServiceImpl extends ServiceImpl<RulesMapper, RulesDO> implements RulesService {

    @Resource
    private RulesMapper rulesMapper;

    @Resource
    private WarnConditionService warningConditionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInfo(RulesSaveBO saveBO){
        if (CollUtil.isEmpty(saveBO.getConditionList())) {
            throw new RuntimeException("预警条件不能为空");
        }
        RulesDO entity = new RulesDO();
        BeanUtils.copyProperties(saveBO, entity);
        rulesMapper.insert(entity);
        List<WarnConditionDO> warnConditions = BeanUtil.copyToList(saveBO.getConditionList(), WarnConditionDO.class);
        warnConditions.stream().forEach(item -> item.setRulesId(entity.getId()));
        return warningConditionService.saveBatch(warnConditions);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delInfo(Long id){
        //1.删除预警条件
        warningConditionService.remove(new LambdaUpdateWrapper<WarnConditionDO>().eq(WarnConditionDO::getRulesId, id));
        //2.删除规则
        return SqlHelper.retBool(rulesMapper.deleteById(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInfo(RulesUpdateBO updateBO) {
        // 1. 更新规则基本信息
        RulesDO entity = new RulesDO();
        BeanUtils.copyProperties(updateBO, entity);
        rulesMapper.updateById(entity);
        // 2. 处理预警条件
        List<WarnConditionUpdateBO> newConditions = updateBO.getConditionList();
        if (CollUtil.isEmpty(newConditions)) {
            throw new RuntimeException("预警条件不能为空");
        }
        // 2.1 查询数据库中现有的条件
        List<WarnConditionDO> oldConditions = warningConditionService.list(new LambdaQueryWrapper<WarnConditionDO>()
                        .eq(WarnConditionDO::getRulesId, updateBO.getId()));
        Map<Long, WarnConditionDO> oldConditionMap = oldConditions.stream()
                .collect(Collectors.toMap(WarnConditionDO::getId, Function.identity()));

        // 2.2 遍历新条件列表，处理新增/更新
        List<WarnConditionDO> conditionsToSave = new ArrayList<>();
        for (WarnConditionUpdateBO newCondition : newConditions) {
            WarnConditionDO condition = new WarnConditionDO();
            BeanUtils.copyProperties(newCondition, condition);
            condition.setRulesId(updateBO.getId());
            if (newCondition.getId() != null && oldConditionMap.containsKey(newCondition.getId()) && oldConditionMap.get(newCondition.getId()).equals(condition)) {
                continue;
            } else {
                condition.setId(null);
            }
            conditionsToSave.add(condition);
        }
        // 2.3 批量保存
        warningConditionService.saveOrUpdateBatch(conditionsToSave);

        // 2.4 删除多余的条件
        Set<Long> newConditionIds = newConditions.stream()
                .map(WarnConditionUpdateBO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        warningConditionService.remove(new LambdaQueryWrapper<WarnConditionDO>()
                        .eq(WarnConditionDO::getRulesId, updateBO.getId())
                        .notIn(CollUtil.isNotEmpty(newConditionIds), WarnConditionDO::getId, newConditionIds));
        return true;
    }

    @Override
    public RulesDetailVO getInfo(Long id){
        RulesDO entity = rulesMapper.selectById(id);
        RulesDetailVO rulesDetail = RulesDetailVO.of(entity);
        List<WarnConditionDO> conditionList = warningConditionService.list(new LambdaQueryWrapper<WarnConditionDO>().eq(WarnConditionDO::getRulesId, id));
        rulesDetail.setConditionList(conditionList);
        return rulesDetail;
    }

    @Override
    public IPage<RulesPageVO> getPageInfo(RulesQueryParamsBO queryParamsBO){
        return rulesMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(RulesPageVO::of);
    }

}
