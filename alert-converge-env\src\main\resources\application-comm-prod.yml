# 存放【prod】独享配置
pub:
  swagger:
    api-enabled: false

# redis
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: HZEDHikariCP
      minimum-idle: 2
      maximum-pool-size: 100
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
    url: ***********************************************************************************************************************************************************************************
    username: u_alertdb
    password: Mfhcnkieu284nc3
  redis:
    database: 57
    host: hezhong88.redis.rds.aliyuncs.com
    port: 4467
    password: p5SjpHRk8RdDGpkT
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        min-idle: 1
        max-idle: 8
        max-wait: 3000ms
        shutdown-timeout: 5000ms
      cluster:
        refresh:
          adaptive: true
          period: 10000ms

# jetcache
jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  hidePackages: com.hzed.pub
  local:
    default:
      type: caffeine
      limit: 5000
      keyConvertor: fastjson2
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson2
      valueEncoder: bean:jetJacksonValueEncoder
      valueDecoder: bean:jetJacksonValueDecoder
      poolConfig:
        minIdle: 1
        maxIdle: 8
        maxTotal: 8
      uri: redis://<EMAIL>:4467/57?timeout=5s

hzed:
  sso:
    systemDomainCode: warning-admin
    sso-service-url: https://sso-service.yichenxuanshiye.com
    access-denied-handle-type: json
    filter-chain-definition-map[/alertEvent/submit]: anon