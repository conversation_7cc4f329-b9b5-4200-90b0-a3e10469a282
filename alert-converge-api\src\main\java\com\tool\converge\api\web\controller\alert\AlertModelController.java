package com.tool.converge.api.web.controller.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tool.converge.business.alert.AlertModelService;
import io.swagger.v3.oas.annotations.Operation;
import com.hzed.structure.log.annotation.PrintLog;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.repository.domain.alert.bo.AlertModelSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelPageVO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Set;

/**
 * <p>
 * 预警模型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:01
 */
@Tag(name = "预警模型")
@RestController
@RequestMapping("/alertModel")
public class AlertModelController {

    @Resource
    private AlertModelService alertModelService;

    @ApiResponse
    @Operation(summary = "分页查询预警模型")
    @PrintLog("分页查询预警模型")
    @GetMapping("/page")
    public IPage<AlertModelPageVO> page(@Valid @ParameterObject AlertModelQueryParamsBO queryParamsBO) {
        return alertModelService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "获取所有的预警名称")
    @PrintLog("获取所有的预警名称")
    @GetMapping("/getAllModelName")
    public Set<String> getAllModelName() {
        return alertModelService.getAllModelName();
    }

    @ApiResponse
    @Operation(summary = "保存预警模型")
    @PrintLog("保存预警模型")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid AlertModelSaveBO saveBO) {
        return alertModelService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改预警模型")
    @PrintLog("修改预警模型")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid AlertModelUpdateBO updateBO) {
        return alertModelService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除预警模型")
    @PrintLog("删除预警模型")
    @PostMapping("/del/{id}")
    public Boolean del(@PathVariable("id") Long id) {
        return alertModelService.delInfo(id);
    }
}
