package com.tool.converge.business.alert;

import cn.hutool.http.server.HttpServerResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.alert.bo.*;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.vo.AlertEventDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertEventPageVO;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;

/**
 * <p>
 * 预警事件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:00
 */
public interface AlertEventService extends IService<AlertEventDO> {

    /**
     * 添加信息
     *
     * @param submitEventBO
     * @return
     */
    LocalDateTime submit(AlertEventSdkBO submitEventBO);

    /**
     * 添加信息
     *
     * @param submitEventBO
     * @param submitTime
     * @return
     */
    void asyncInsertAlertEvent(AlertEventSdkBO submitEventBO, LocalDateTime submitTime);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(AlertEventUpdateBO updateBO);

    /**
     * 导出
     *
     * @param alertEventExportBo
     * @param httpServletResponse
     * @return
     */
    void export(AlertEventExportBo alertEventExportBo, HttpServletResponse httpServletResponse);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<AlertEventPageVO> getPageInfo(AlertEventQueryParamsBO queryParamsBO);

}
