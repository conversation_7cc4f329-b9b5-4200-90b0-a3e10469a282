package com.tool.converge.business.rules;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.rules.bo.RulesSaveBO;
import com.tool.converge.repository.domain.rules.bo.RulesUpdateBO;
import com.tool.converge.repository.domain.rules.bo.RulesQueryParamsBO;
import com.tool.converge.repository.domain.rules.vo.RulesDetailVO;
import com.tool.converge.repository.domain.rules.vo.RulesPageVO;

/**
 * <p>
 * 规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
public interface RulesService extends IService<RulesDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(RulesSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(RulesUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    RulesDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<RulesPageVO> getPageInfo(RulesQueryParamsBO queryParamsBO);

}
