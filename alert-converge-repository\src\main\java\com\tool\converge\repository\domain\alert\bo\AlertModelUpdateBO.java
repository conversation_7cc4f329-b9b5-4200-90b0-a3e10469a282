package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警模型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21 11:32:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelUpdateBO对象", description = "预警模型")
public class AlertModelUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "模型id")
    private Long id;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "规则id")
    private Long ruleId;

    @Schema(description = "描述")
    private String description;

}
