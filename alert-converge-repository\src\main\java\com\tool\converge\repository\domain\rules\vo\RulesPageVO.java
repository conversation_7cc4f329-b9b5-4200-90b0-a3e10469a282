package com.tool.converge.repository.domain.rules.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "RulesPageVO对象", description = "规则表")
public class RulesPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "规则id")
    private Long id;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则编码")
    private String ruleCode;

    @Schema(description = "规则状态：0-启动 1-关闭")
    private String ruleStatus;

    @Schema(description = "规则匹配：0-满足任一条件 1-满足所有条件")
    private String ruleMatching;

    @Schema(description = "预警级别")
    private String warnLevel;

    @Schema(description = "预警级别名称")
    private String warnLevelName;

    @Schema(description = "应用资方(多个资方之间逗号隔开)")
    private String applyInvestor;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static RulesPageVO of(RulesDO entity){
        if(entity == null){
            return null;
        }
        RulesPageVO pageVO = new RulesPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
