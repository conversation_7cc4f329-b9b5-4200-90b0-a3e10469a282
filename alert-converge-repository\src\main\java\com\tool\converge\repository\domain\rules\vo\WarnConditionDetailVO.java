package com.tool.converge.repository.domain.rules.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 预警条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "WarnConditionDetailVO对象", description = "预警条件表")
public class WarnConditionDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;
    
    @Schema(description = "条件id")
    private Long id;

    @Schema(description = "规则id")
    private Long rulesId;

    @Schema(description = "设置项")
    private String settingItem;

    @Schema(description = "运算符")
    private String operator;

    @Schema(description = "赋值项")
    private Integer assignmentItem;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static WarnConditionDetailVO of(WarnConditionDO entity){
        if(entity == null){
            return null;
        }
        WarnConditionDetailVO detailVO = new WarnConditionDetailVO();
        BeanUtils.copyProperties(entity,detailVO);
        return detailVO;
    }

}
