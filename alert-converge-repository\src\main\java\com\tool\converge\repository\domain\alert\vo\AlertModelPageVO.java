package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 预警模型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21 11:32:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelPageVO对象", description = "预警模型")
public class AlertModelPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "模型id")
    private Long id;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "模型编码")
    private String modelCode;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "规则id")
    private Long ruleId;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    public static AlertModelPageVO of(AlertModelDO entity){
        if(entity == null){
            return null;
        }
        AlertModelPageVO pageVO = new AlertModelPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
