<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.alert.AlertModelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.alert.db.AlertModelConfigDO">
        <id column="id" property="id" />
        <result column="model_id" property="modelId" />
        <result column="frequency" property="frequency" />
        <result column="warn_type" property="warnType" />
        <result column="warn_content" property="warnContent" />
        <result column="webhook" property="webhook" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_id, frequency, warn_type, warn_content, webhook, create_time, update_time, creator, updater, deleted
    </sql>

</mapper>
