package com.tool.converge.repository.domain.rules.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rules")
@Schema(name = "RulesDO对象", description = "规则表")
public class RulesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "规则id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "规则名称")
    @TableField("`rule_name`")
    private String ruleName;

    @Schema(description = "规则编码")
    @TableField("`rule_code`")
    private String ruleCode;

    @Schema(description = "规则状态：0-启动 1-关闭")
    @TableField("`rule_status`")
    private String ruleStatus;

    @Schema(description = "规则匹配：0-满足任一条件 1-满足所有条件")
    @TableField("`rule_matching`")
    private String ruleMatching;

    @Schema(description = "预警级别")
    @TableField("`warn_level`")
    private String warnLevel;

    @Schema(description = "应用资方(多个资方之间逗号隔开)")
    @TableField("`apply_investor`")
    private String applyInvestor;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
