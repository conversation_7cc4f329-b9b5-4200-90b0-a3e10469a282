package com.tool.converge.business.alert.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.alert.AlertEventService;
import com.tool.converge.business.alert.AlertModelConfigService;
import com.tool.converge.business.alert.AlertModelNotifiersService;
import com.tool.converge.business.alert.AlertModelService;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryExtendSaveBo;
import com.tool.converge.repository.domain.alert.bo.AlertModelUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO;
import com.tool.converge.repository.domain.alert.vo.AlertModelDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelPageVO;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import com.tool.converge.repository.mapper.alert.AlertModelMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警模型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:01
 */
@Slf4j
@Service
public class AlertModelServiceImpl extends ServiceImpl<AlertModelMapper, AlertModelDO> implements AlertModelService {

    @Resource
    private AlertModelMapper alertModelMapper;

    @Resource
    private AlertEventService alertEventService;
    @Resource
    private AlertModelConfigService alertModelConfigService;

    @Resource
    private AlertModelNotifiersService alertModelNotifiersService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInfo(AlertModelQueryExtendSaveBo saveBO){
        AlertModelDO entity = new AlertModelDO();
        if (existsModelCode(saveBO.getModelCode())) {
            throw new ServiceException("预警模型编码已存在");
        }
        BeanUtils.copyProperties(saveBO, entity);
        alertModelMapper.insert(entity);
        if (entity.getId() == null) {
            throw new ServiceException("预警模型编码保存失败");
        }
        if (saveBO.getWarned()) {
            AlertModelConfigDO configDO = new AlertModelConfigDO();
            BeanUtils.copyProperties(saveBO, configDO);
            configDO.setModelId(entity.getId());
            alertModelConfigService.save(configDO);

            if (CollectionUtil.isNotEmpty(saveBO.getNotifiers())) {
                List<AlertModelNotifiersDO> notifiersDos = saveBO.getNotifiers().stream().map(id -> {
                    AlertModelNotifiersDO alertModelNotifiersDO = new AlertModelNotifiersDO();
                    alertModelNotifiersDO.setConfigId(configDO.getId());
                    alertModelNotifiersDO.setUserId(id);
                    return alertModelNotifiersDO;
                }).collect(Collectors.toList());
                alertModelNotifiersService.saveBatch(notifiersDos);
            }
        }
        return true;
    }

    @Override
    public Set<String> getAllModelName() {
        List<AlertModelDO> alertModelDoS = alertModelMapper.selectList(new LambdaQueryWrapper<AlertModelDO>().select(AlertModelDO::getModelName));
        return alertModelDoS.stream().map(AlertModelDO::getModelName).collect(Collectors.toSet());
    }

    @Override
    public Boolean delInfo(Long id){
        AlertModelDO alertModelDO = alertModelMapper.selectById(id);
        if (alertModelDO == null) {
            throw new ServiceException("该预警模型不存在");
        }
        if (alertEventService.exists(new LambdaQueryWrapper<AlertEventDO>().eq(AlertEventDO::getModelCode, alertModelDO.getModelCode()))) {
            throw new ServiceException("该预警模型存在引用预警事件，不支持删除");
        }
        return SqlHelper.retBool(alertModelMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(AlertModelUpdateBO updateBO){
        AlertModelDO entity = new AlertModelDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(alertModelMapper.updateById(entity));
    }

    @Override
    public AlertModelDetailVO getInfo(Long id){
        AlertModelDO entity = alertModelMapper.selectById(id);
        return AlertModelDetailVO.of(entity);
    }

    @Override
    public IPage<AlertModelPageVO> getPageInfo(AlertModelQueryParamsBO queryParamsBO){
        if (StringUtils.isBlank(queryParamsBO.getOrderFields())) {
            queryParamsBO.setOrderFields("createTime");
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderRules())) {
            queryParamsBO.setOrderRules("desc");
        }
        return alertModelMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(AlertModelPageVO::of);
    }

    private Boolean existsModelCode(String modelCode) {
        List<AlertModelDO> alertModelDoS = alertModelMapper.selectList(new LambdaQueryWrapper<AlertModelDO>().eq(AlertModelDO::getModelCode, modelCode));
        return !alertModelDoS.isEmpty();
    }
}
