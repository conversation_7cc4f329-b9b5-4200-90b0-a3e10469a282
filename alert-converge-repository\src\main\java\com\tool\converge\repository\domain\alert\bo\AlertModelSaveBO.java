package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <p>
 * 预警模型
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 16:33:21
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelSaveBO对象", description = "预警模型")
public class AlertModelSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "预警名称")
    private String modelName;


    @Schema(description = "模型编码")
    private String modelCode;


    @Schema(description = "业务类型")
    private String businessType;


    @Schema(description = "预警类型")
    private String alertType;


    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;


    @Schema(description = "规则id")
    private Long ruleId;


    @Schema(description = "是否生成预警消息 0为不生成，1为生成")
    private Boolean warned;


    @Schema(description = "描述")
    private String description;


}
