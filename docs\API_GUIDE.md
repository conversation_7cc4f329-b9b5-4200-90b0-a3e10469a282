# API接口文档

## 1. 接口概览

Alert Converge 预警系统提供RESTful风格的API接口，支持预警事件管理、预警模型管理、数据源管理等核心功能。

### 1.1 基础信息

- **Base URL**: `http://host:port/alert-converge-api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **API文档**: `http://host:port/alert-converge-api/doc.html`

### 1.2 通用响应格式

所有接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-04T10:30:00"
}
```

### 1.3 分页响应格式

分页查询接口的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 2. 预警事件接口

### 2.1 提交预警事件

**接口地址**: `POST /alertEvent/submit`

**接口描述**: 外部系统通过此接口提交预警事件

**请求参数**:
```json
{
  "eventId": "EVT20250104001",
  "systemId": "SYSTEM001", 
  "modelId": "MODEL001",
  "platformName": "业务平台名称",
  "period": "202501",
  "reason": "预警触发原因",
  "md5": "unique_md5_hash",
  "serviceNo": "BIZ20250104001",
  "indexValue": "100.5",
  "payload": "{\"key\":\"value\"}",
  "state": "PENDING"
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| eventId | String | 是 | 事件编号，SDK生成 |
| systemId | String | 是 | 系统编号 |
| modelId | String | 是 | 模型编号 |
| platformName | String | 是 | 第三方平台名称 |
| period | String | 否 | 期次 |
| reason | String | 是 | 预警原因 |
| md5 | String | 是 | 唯一标识，用于去重 |
| serviceNo | String | 是 | 业务唯一编号 |
| indexValue | String | 是 | 指标值 |
| payload | String | 否 | 扩展字段，JSON格式 |
| state | String | 是 | 状态值 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "2025-01-04T10:30:00",
  "timestamp": "2025-01-04T10:30:00"
}
```

### 2.2 分页查询预警事件

**接口地址**: `GET /alertEvent/page`

**接口描述**: 分页查询预警事件列表

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| systemCode | String | 否 | 系统编号 |
| modelCode | String | 否 | 模型编号 |
| startTime | String | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |

**请求示例**:
```
GET /alertEvent/page?current=1&size=10&systemCode=SYSTEM001&startTime=2025-01-01 00:00:00&endTime=2025-01-31 23:59:59
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "eventId": "EVT20250104001",
        "systemCode": "SYSTEM001",
        "systemName": "业务系统A",
        "modelCode": "MODEL001",
        "modelName": "风险预警模型",
        "platformName": "业务平台",
        "reason": "指标超阈值",
        "serviceNo": "BIZ20250104001",
        "indexValue": "100.5",
        "state": "PENDING",
        "createTime": "2025-01-04T10:30:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2.3 导出预警事件

**接口地址**: `POST /alertEvent/export`

**接口描述**: 导出预警事件数据到Excel文件

**请求参数**:
```json
{
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-01-31 23:59:59",
  "systemCode": "SYSTEM001",
  "modelCode": "MODEL001",
  "ids": [1, 2, 3]
}
```

**参数说明**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |
| systemCode | String | 否 | 系统编号 |
| modelCode | String | 否 | 模型编号 |
| ids | List<Long> | 否 | 指定导出的记录ID |

**响应**: 直接返回Excel文件流

## 3. 预警模型接口

### 3.1 分页查询预警模型

**接口地址**: `GET /alertModel/page`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页大小 |
| modelName | String | 否 | 模型名称 |
| businessType | String | 否 | 业务类型 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "records": [
      {
        "id": 1,
        "modelName": "风险预警模型",
        "modelCode": "MODEL001",
        "businessType": "RISK",
        "alertType": "HIGH",
        "related": true,
        "description": "用于风险监控的预警模型"
      }
    ]
  }
}
```

### 3.2 保存预警模型

**接口地址**: `POST /alertModel/save`

**请求参数**:
```json
{
  "modelName": "新预警模型",
  "modelCode": "MODEL002", 
  "businessType": "BUSINESS",
  "alertType": "MEDIUM",
  "related": false,
  "description": "业务预警模型"
}
```

### 3.3 修改预警模型

**接口地址**: `POST /alertModel/update`

**请求参数**:
```json
{
  "id": 1,
  "modelName": "更新后的模型名称",
  "description": "更新后的描述"
}
```

### 3.4 删除预警模型

**接口地址**: `POST /alertModel/del/{id}`

**路径参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | Long | 是 | 模型ID |

### 3.5 获取所有预警名称

**接口地址**: `GET /alertModel/getAllModelName`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": ["风险预警模型", "业务预警模型", "系统预警模型"]
}
```

## 4. 数据源接口

### 4.1 分页查询数据源

**接口地址**: `GET /dataSource/page`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页大小 |
| systemName | String | 否 | 系统名称 |
| sourceType | String | 否 | 数据源类型 |

### 4.2 保存数据源

**接口地址**: `POST /dataSource/save`

**请求参数**:
```json
{
  "sourceType": "API",
  "systemName": "业务系统A",
  "systemCode": "SYSTEM001",
  "description": "主要业务系统"
}
```

### 4.3 修改数据源

**接口地址**: `POST /dataSource/update`

### 4.4 删除数据源

**接口地址**: `POST /dataSource/del/{id}`

### 4.5 判断系统编号是否存在

**接口地址**: `GET /dataSource/exists`

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| systemCode | String | 是 | 系统编号 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

### 4.6 获取所有系统名称

**接口地址**: `GET /dataSource/getAllSystemName`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": ["业务系统A", "业务系统B", "监控系统"]
}
```

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 6. SDK使用示例

### 6.1 Java SDK示例

```java
// 创建预警事件
AlertEventSdkBO event = AlertEventSdkBO.builder()
    .eventId("EVT" + System.currentTimeMillis())
    .systemId("SYSTEM001")
    .modelId("MODEL001")
    .platformName("业务平台")
    .reason("指标异常")
    .md5(DigestUtils.md5Hex("unique_key"))
    .serviceNo("BIZ" + System.currentTimeMillis())
    .indexValue("100.5")
    .state("PENDING")
    .build();

// 调用API
RestTemplate restTemplate = new RestTemplate();
String url = "http://localhost:8000/alert-converge-api/alertEvent/submit";
ResponseEntity<ApiResult> response = restTemplate.postForEntity(url, event, ApiResult.class);
```

### 6.2 cURL示例

```bash
# 提交预警事件
curl -X POST "http://localhost:8000/alert-converge-api/alertEvent/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "eventId": "EVT20250104001",
    "systemId": "SYSTEM001",
    "modelId": "MODEL001", 
    "platformName": "业务平台",
    "reason": "指标异常",
    "md5": "unique_md5_hash",
    "serviceNo": "BIZ20250104001",
    "indexValue": "100.5",
    "state": "PENDING"
  }'

# 查询预警事件
curl -X GET "http://localhost:8000/alert-converge-api/alertEvent/page?current=1&size=10"
```

## 7. 注意事项

1. **去重机制**: 相同md5值的事件会被认为是重复事件，系统会自动去重
2. **异步处理**: 预警事件提交后会异步处理，接口立即返回提交时间
3. **分页限制**: 单次查询最大返回1000条记录
4. **导出限制**: 单次导出最大支持10000条记录
5. **时间格式**: 所有时间参数使用格式：yyyy-MM-dd HH:mm:ss
6. **字符编码**: 所有接口使用UTF-8编码
7. **超时设置**: 建议客户端设置30秒超时时间
