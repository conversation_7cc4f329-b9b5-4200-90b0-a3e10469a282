package com.tool.converge.repository.domain.alert.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
  * <AUTHOR>
  * @date 2025/8/5 11:37
  * @Description 模型查询
  * @MethodName
  */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AlertModelQueryExtendSaveBo extends AlertModelSaveBO{

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;


    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;


    @Schema(description = "预警内容")
    private String warnContent;


    @Schema(description = "钉钉群地址")
    private String webhook;

    @Schema(description = "通知人员ID")
    private List<Long> notifiers;
}
