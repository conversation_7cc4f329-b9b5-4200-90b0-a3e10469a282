# 存放【testA】独享配置
# redis暂时没有配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: HZEDHikariCP
      minimum-idle: 2
      maximum-pool-size: 10
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
    url: *******************************************************************************************************************************************************************************
    username: u_alertdb_dev
    password: MJci8812hhGGjlksahgFG
  redis:
    database: 237
    host: redis.qmwallet.vip
    port: 4467
    password: smu72fjs9bbshzp
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        min-idle: 1
        max-idle: 8
        max-wait: 3000ms
        shutdown-timeout: 5000ms
      cluster:
        refresh:
          adaptive: true
          period: 10000ms

# jetcache
jetcache:
  statIntervalMinutes: 0
  areaInCacheName: false
  hidePackages: com.hzed.pub
  local:
    default:
      type: caffeine
      limit: 5000
      keyConvertor: fastjson2
      expireAfterWriteInMillis: 100000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson2
      valueEncoder: bean:jetJacksonValueEncoder
      valueDecoder: bean:jetJacksonValueDecoder
      poolConfig:
        minIdle: 1
        maxIdle: 8
        maxTotal: 8
      uri: redis://<EMAIL>:4467/0?timeout=5s

# 集成组件配置
pub:
  mybatis-plus:
    show-sql-log: true
  block:
    sql:
      db-name: alertdb
      enabled: true
      group-name: 工具开发组
      project-name: 预警系统
      # 禅道需求链接,方便SQL关联需求 (每次需求开发，都需要更新该链接,如果无相关需求填:default)
      tag-link: default
      # 这个标签很重要，需要开发阶段就确认好，保证唯一。每次需求开发都要更新
      tag-name: task-view-132998

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 钉钉配置
dingtalk:
  app-key: dingzdbsspnvbrcddwzs
  app-secret: ljf0G7Z7_5LOQaJ-hI_45BwfxA3cYb4gW6apoT8Z41BmvKZuP3F2LUH6h6R8Zgjp