package com.tool.converge.repository.domain.rules.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "RulesQueryParamsBO对象", description = "规则表")
public class RulesQueryParamsBO extends PageParamsBO<RulesDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "规则id")
    private Long id;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则编码")
    private String ruleCode;

    @Schema(description = "规则状态：0-启动 1-关闭")
    private String ruleStatus;

    @Schema(description = "规则匹配：0-满足任一条件 1-满足所有条件")
    private String ruleMatching;

    @Schema(description = "预警级别")
    private String warnLevel;

    @Schema(description = "应用资方(多个资方之间逗号隔开)")
    private String applyInvestor;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<RulesDO> queryWrapper() {

        LambdaQueryWrapper<RulesDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,RulesDO::getId,id);

        query.like(StringUtils.isNotBlank(ruleName),RulesDO::getRuleName,ruleName);

        query.like(StringUtils.isNotBlank(ruleCode),RulesDO::getRuleCode,ruleCode);

        query.eq(StringUtils.isNotBlank(ruleStatus) && !("-1").equals(ruleStatus),RulesDO::getRuleStatus,ruleStatus);

        query.eq(StringUtils.isNotBlank(ruleMatching),RulesDO::getRuleMatching,ruleMatching);

        query.eq(StringUtils.isNotBlank(warnLevel),RulesDO::getWarnLevel,warnLevel);

        query.eq(StringUtils.isNotBlank(applyInvestor),RulesDO::getApplyInvestor,applyInvestor);

        query.ge(createTimeStart != null, RulesDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, RulesDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, RulesDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, RulesDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),RulesDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),RulesDO::getUpdater,updater);

        query.eq(deleted!=null,RulesDO::getDeleted,deleted);

        return query;
    }
}
