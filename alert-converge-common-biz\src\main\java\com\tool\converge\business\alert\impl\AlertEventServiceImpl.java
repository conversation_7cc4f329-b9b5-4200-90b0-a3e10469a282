package com.tool.converge.business.alert.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.redis.util.RedisUtil;
import com.tool.converge.business.alert.AlertEventService;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.common.constant.KeyConstant;
import com.tool.converge.common.utils.ExcelUtils;
import com.tool.converge.repository.domain.alert.bo.*;
import com.tool.converge.repository.domain.alert.vo.AlertEventExportVO;
import com.tool.converge.repository.domain.alert.vo.AlertEventPageVO;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import com.tool.converge.repository.mapper.alert.AlertEventMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 预警事件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:00
 */
@Slf4j
@Service
public class AlertEventServiceImpl extends ServiceImpl<AlertEventMapper, AlertEventDO> implements AlertEventService {

    @Resource
    private AlertEventMapper alertEventMapper;

    @Resource
    @Lazy
    private AlertEventService self;

    @Resource
    private SysDictValueService sysDictValueService;

    @Override
    public LocalDateTime submit(AlertEventSdkBO submitEventBO) {
        List<AlertEventDO> alertEventDoS = alertEventMapper.selectList(new LambdaQueryWrapper<AlertEventDO>().eq(AlertEventDO::getMd5, submitEventBO.getMd5()));
        if (CollectionUtil.isNotEmpty(alertEventDoS)) {
                return alertEventDoS.get(0).getCreateTime();
        }
        LocalDateTime now = LocalDateTime.now();
        self.asyncInsertAlertEvent(submitEventBO,now);
        return now;
    }

    @Override
    @Async("Alert-Event-Submit")
    public void asyncInsertAlertEvent(AlertEventSdkBO submitEventBO, LocalDateTime submitTime) {
        String key = KeyConstant.ALERT_EVENT_SUBMIT + submitEventBO.getMd5();
        if (Boolean.TRUE.equals(RedisUtil.exists(key))) {
            log.error("已存在正在上报事件，事件业务编号:{},期次:{}, 原因:{}", submitEventBO.getServiceNo(), submitEventBO.getPeriod(), submitEventBO.getReason());
            throw new ServiceException("已存在正在上报事件，事件业务编号:" + submitEventBO.getServiceNo());
        }
        try {
            RedisUtil.setNx(key, "", KeyConstant.THREE_HUNDRED, TimeUnit.SECONDS);
            AlertEventDO entity = new AlertEventDO();
            BeanUtils.copyProperties(submitEventBO, entity);
            entity.setModelCode(submitEventBO.getModelId());
            entity.setSystemCode(submitEventBO.getSystemId());
            entity.setCreateTime(submitTime);
            if (!SqlHelper.retBool(alertEventMapper.insert(entity))) {
                log.error("异步插入预警事件失败，eventId={},serviceNo={}", entity.getEventId(), entity.getServiceNo());
            }
        } catch (Exception e) {
            log.error("异步插入预警事件失败，entity={}", submitEventBO.getServiceNo(), e);
        }finally {
            RedisUtil.del(key);
        }
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(alertEventMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(AlertEventUpdateBO updateBO) {
        AlertEventDO entity = new AlertEventDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(alertEventMapper.updateById(entity));
    }

    @Override
    public void export(AlertEventExportBo alertEventExportBo, HttpServletResponse httpServletResponse) {
        List<AlertEventDO> alertEventDoS = alertEventMapper.selectEventDetail(AlertEventDO.builder().ids(alertEventExportBo.getIds()).deleted(false).build());
        // 业务类型
        Map<String, String> businessType = sysDictValueService.listByKeyName("business_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 预警类型
        Map<String, String> alertType = sysDictValueService.listByKeyName("alert_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 状态值
        Map<String, String> alertState = sysDictValueService.listByKeyName("alert_state").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
        // 事件状态
        Map<String, String> eventStatus = sysDictValueService.listByKeyName("alert_event_status").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));

        // 循环设置参数
        List<AlertEventExportVO> eventExportVoS = alertEventDoS.stream().map(eventDO -> {
            AlertEventExportVO alertEventExportVO = new AlertEventExportVO();
            BeanUtils.copyProperties(eventDO, alertEventExportVO);
            alertEventExportVO.setState(alertState.get(alertEventExportVO.getState()));
            alertEventExportVO.setEventStatus(eventStatus.get(alertEventExportVO.getState()));
            alertEventExportVO.setBusinessType(businessType.get(alertEventExportVO.getBusinessType()));
            alertEventExportVO.setAlertType(alertType.get(alertEventExportVO.getAlertType()));
            return alertEventExportVO;
        }).collect(Collectors.toList());
        //导出
        ExcelUtils.writeExcel(eventExportVoS, AlertEventExportVO.class, alertEventExportBo.getOrderByColumns(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")), httpServletResponse);
    }


    @Override
    public IPage<AlertEventPageVO> getPageInfo(AlertEventQueryParamsBO queryParamsBO) {
        AlertEventDO alertEventDO =new AlertEventDO();
        BeanUtils.copyProperties(queryParamsBO, alertEventDO);
        if (alertEventDO.getDeleted() == null) {
            alertEventDO.setDeleted(false);
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderFields())) {
            queryParamsBO.setOrderFields("createTime");
        }
        if (StringUtils.isBlank(queryParamsBO.getOrderRules())) {
            queryParamsBO.setOrderRules("desc");
        }
        IPage<AlertEventPageVO> convert = alertEventMapper.selectEventPage(queryParamsBO.pageInfo(), alertEventDO).convert(AlertEventPageVO::of);
        if (CollectionUtil.isNotEmpty(convert.getRecords())) {
            List<AlertEventPageVO> records = convert.getRecords();
             // 业务类型
            Map<String, String> businessType = sysDictValueService.listByKeyName("business_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 预警类型
            Map<String, String> alertType = sysDictValueService.listByKeyName("alert_type").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 状态值
            Map<String, String> alertState = sysDictValueService.listByKeyName("alert_state").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            // 事件状态
            Map<String, String> eventStatus = sysDictValueService.listByKeyName("alert_event_status").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            records.forEach(eventVo -> {
                eventVo.setState(alertState.get(eventVo.getState()));
                eventVo.setEventStatus(eventStatus.get(eventVo.getState()));
                eventVo.setAlertType(alertType.get(eventVo.getAlertType()));
                eventVo.setBusinessType(businessType.get(eventVo.getBusinessType()));
            });
        }
        return convert;
    }

}
