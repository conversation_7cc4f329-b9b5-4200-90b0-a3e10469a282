package com.tool.converge.repository.domain.rules.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警条件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04 16:55:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "WarnConditionUpdateBO对象", description = "预警条件表")
public class WarnConditionUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "条件id")
    private Long id;

    @Schema(description = "规则id")
    private Long rulesId;

    @Schema(description = "设置项")
    private String settingItem;

    @Schema(description = "运算符")
    private String operator;

    @Schema(description = "赋值项")
    private Integer assignmentItem;

}
