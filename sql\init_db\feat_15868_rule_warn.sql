CREATE TABLE `t_alert_message` (
           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',

-- 基础信息字段（与现有表保持完全一致）
           `event_id` varchar(20) NOT NULL COMMENT '预警事件编号，SDK生成（对应t_alert_event.event_id）',
           `model_code` varchar(10) NOT NULL COMMENT '模型编码（对应t_alert_model.model_code）',
           `model_name` varchar(100) NOT NULL COMMENT '预警名称（对应t_alert_model.model_name）',
           `alert_type` varchar(10) NOT NULL COMMENT '预警类型（对应t_alert_model.alert_type）',
           `platform_name` varchar(100) DEFAULT NULL COMMENT '第三方名称（对应t_alert_event.platform_name）',
           `period` varchar(10) DEFAULT NULL COMMENT '期次（对应t_alert_event.period）',
           `service_no` varchar(100) DEFAULT NULL COMMENT '业务唯一编号（对应t_alert_event.service_no）',
           `business_type` varchar(20) NOT NULL COMMENT '业务类型（对应t_alert_model.business_type）',
           `state` varchar(50) DEFAULT NULL COMMENT '状态值， 例如：单状态/还款状态/ 支付状态/推送状态（对应t_alert_event.state）',
           `index_value` varchar(100) DEFAULT NULL COMMENT '指标值（对应t_alert_event.index_value）',
           `reason` varchar(255) DEFAULT NULL COMMENT '原因（对应t_alert_event.reason）',
           `payload` text DEFAULT NULL COMMENT '拓展字段（对应t_alert_event.payload）',

-- 规则相关字段
           `rule_id` bigint DEFAULT NULL COMMENT '规则id（对应t_alert_model.rule_id）',
           `rule_name` varchar(200) DEFAULT NULL COMMENT '预警规则（对应t_rules.rule_name）',
           `related` bit DEFAULT b'0' COMMENT '是否关联规则 0为不关联，1为关联（对应t_alert_model.related）',
           `warn_level` varchar(20) NOT NULL COMMENT '预警级别（对应t_rules.warn_level）',

-- 系统相关字段
           `system_code` varchar(20) NOT NULL COMMENT '系统编号（对应t_data_source.system_code）',
           `system_name` varchar(100) NOT NULL COMMENT '系统名称（对应t_data_source.system_name）',
           `alert_time` datetime NOT NULL COMMENT '预警事件时间/上报时间',

-- 配置相关字段（对应t_alert_model_config）
           `config_id` bigint DEFAULT NULL COMMENT '预警配置id（对应t_alert_model_config.id）',
           `frequency` int(1) DEFAULT NULL COMMENT '预警频率 0为不限制（对应t_alert_model_config.frequency）',
           `warn_type` int(1) DEFAULT NULL COMMENT '通知方式 1:钉钉，2:短信，3:钉钉+短信（对应t_alert_model_config.warn_type）',

-- 通知相关字段（基于t_alert_model_notifiers关联查询得出）
           `notification_users` varchar(1024) DEFAULT NULL COMMENT '通知人员（从t_alert_model_notifiers关联查询得出）',


-- 标准字段（与现有表完全一致）
           `create_time` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
           `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
           `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
           `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
           `deleted` bit DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',

           PRIMARY KEY (`id`) USING BTREE,
           KEY `idx_model_code` (`model_code`) USING BTREE,
           KEY `idx_model_name` (`model_name`) USING BTREE,
           KEY `idx_alert_time` (`alert_time`) USING BTREE,
           KEY `idx_warn_level` (`warn_level`) USING BTREE,
           KEY `idx_platform_name` (`platform_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预警消息表';

ALTER TABLE `t_alert_model`
    MODIFY COLUMN `related` bit(1) NULL DEFAULT 0 COMMENT '是否关联规则 0为不关联，1为关联' AFTER `alert_type`,
    ADD COLUMN `warned` bit(1) NULL DEFAULT 0 COMMENT '是否生成预警消息 0为不生成，1为生成' AFTER `congfig_id`;


CREATE TABLE `t_alert_model_notifiers` (
                                           `user_id` bigint(20) NOT NULL COMMENT '通知用户ID',
                                           `config_id` bigint(20) NOT NULL COMMENT '预警配置ID',
                                           PRIMARY KEY (`user_id`,`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警通知人员表';

CREATE TABLE `t_alert_model_config` (
                                        `id` bigint(20) NOT NULL COMMENT '预警配置id',
                                        `model_id` bigint(20) DEFAULT NULL COMMENT '模型ID',
                                        `frequency` int(1) DEFAULT NULL COMMENT '预警频率 0为不限制',
                                        `warn_type` int(1) DEFAULT NULL COMMENT '预警方式 1:钉钉，2:短信，3:钉钉+短信',
                                        `warn_content` text COMMENT '预警内容',
                                        `webhook` text COMMENT '钉钉群地址',
                                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                        `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
                                        `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除 0未删除 1已删除',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8  COMMENT='预警配置表';