package com.tool.converge.business.alert.impl;

import com.tool.converge.business.alert.AlertModelConfigService;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelConfigQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelConfigDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelConfigPageVO;
import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import com.tool.converge.repository.mapper.alert.AlertModelConfigMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05 10:41:50
 */
@Slf4j
@Service
public class AlertModelConfigServiceImpl extends ServiceImpl<AlertModelConfigMapper, AlertModelConfigDO> implements AlertModelConfigService {

    @Resource
    private AlertModelConfigMapper alertModelConfigMapper;

    @Override
    public Boolean saveInfo(AlertModelConfigSaveBO saveBO){
        AlertModelConfigDO entity = new AlertModelConfigDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(alertModelConfigMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id){
        return SqlHelper.retBool(alertModelConfigMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(AlertModelConfigUpdateBO updateBO){
        AlertModelConfigDO entity = new AlertModelConfigDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(alertModelConfigMapper.updateById(entity));
    }

    @Override
    public AlertModelConfigDetailVO getInfo(Long id){
        AlertModelConfigDO entity = alertModelConfigMapper.selectById(id);
        return AlertModelConfigDetailVO.of(entity);
    }

    @Override
    public IPage<AlertModelConfigPageVO> getPageInfo(AlertModelConfigQueryParamsBO queryParamsBO){
        return alertModelConfigMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(AlertModelConfigPageVO::of);
    }

}
