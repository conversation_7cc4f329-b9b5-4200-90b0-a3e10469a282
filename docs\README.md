# Alert Converge - 预警系统

## 项目概述

Alert Converge 是一个企业级预警事件收集和管理系统，专门用于统一收集、处理和管理来自不同业务系统的预警事件。系统采用微服务架构，支持多环境部署，提供完整的预警事件生命周期管理功能。

## 核心功能

### 🚨 预警事件管理
- **事件收集**: 通过SDK接口统一收集各业务系统的预警事件
- **事件查询**: 支持多维度条件查询和分页展示
- **事件导出**: 支持预警事件数据的批量导出功能
- **事件去重**: 基于MD5算法实现事件去重，避免重复预警

### 📊 预警模型管理
- **模型配置**: 支持预警模型的创建、编辑和管理
- **业务分类**: 按业务类型和预警类型进行分类管理
- **规则关联**: 支持预警模型与业务规则的关联配置

### 🔧 数据源管理
- **多源接入**: 支持多种数据源类型的接入
- **系统管理**: 统一管理接入的业务系统信息
- **权限控制**: 基于系统编码的访问权限控制

### 📈 任务调度
- **定时任务**: 集成XXL-JOB实现分布式任务调度
- **异步处理**: 支持预警事件的异步处理机制
- **监控告警**: 提供任务执行状态监控和异常告警

## 技术架构

### 技术栈
- **框架**: Spring Boot 2.x
- **数据库**: MySQL 5.7+
- **缓存**: Redis + JetCache
- **ORM**: MyBatis-Plus 3.4.3.1
- **任务调度**: XXL-JOB
- **API文档**: Knife4j (OpenAPI 3)
- **监控**: Prometheus + Micrometer

### 模块结构
```
alert-converge/
├── alert-converge-api/          # API接口层
├── alert-converge-job/          # 任务调度模块
├── alert-converge-common/       # 通用工具模块
├── alert-converge-common-biz/   # 业务服务层
├── alert-converge-repository/   # 数据访问层
├── alert-converge-env/          # 环境配置模块
├── docs/                        # 项目文档
└── sql/                         # 数据库脚本
```

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 3.0+

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone <repository-url>
cd alert-converge
```

2. **数据库初始化**
```bash
# 创建数据库
CREATE DATABASE alertdb_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

# 执行初始化脚本
mysql -u username -p alertdb_dev < sql/init_db/init_ddl.sql
mysql -u username -p alertdb_dev < sql/init_db/init_dml.sql
```

3. **配置修改**
修改 `alert-converge-env/src/main/resources/application-comm-dev.yml` 中的数据库和Redis配置：
```yaml
spring:
  datasource:
    url: ***************************************?...
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

4. **启动应用**
```bash
# 启动API服务
cd alert-converge-api
mvn spring-boot:run

# 启动任务调度服务
cd alert-converge-job
mvn spring-boot:run
```

5. **访问应用**
- API服务: http://localhost:8000/alert-converge-api
- API文档: http://localhost:8000/alert-converge-api/doc.html
- 任务调度: http://localhost:6729

## 核心API接口

### 预警事件接口

#### 提交预警事件
```http
POST /alert-converge-api/alertEvent/submit
Content-Type: application/json

{
  "systemCode": "SYSTEM001",
  "modelCode": "MODEL001",
  "platformName": "业务平台",
  "reason": "预警原因",
  "serviceNo": "BIZ20250101001",
  "indexValue": "100",
  "payload": "{\"key\":\"value\"}",
  "state": "PENDING"
}
```

#### 查询预警事件
```http
GET /alert-converge-api/alertEvent/page?current=1&size=10&systemCode=SYSTEM001
```

#### 导出预警事件
```http
POST /alert-converge-api/alertEvent/export
Content-Type: application/json

{
  "startTime": "2025-01-01 00:00:00",
  "endTime": "2025-01-31 23:59:59",
  "systemCode": "SYSTEM001"
}
```

## 部署说明

### 环境配置
系统支持多环境部署：
- **dev**: 开发环境
- **testA**: 测试环境A
- **prod**: 生产环境

### 构建部署
```bash
# 构建项目
mvn clean package -P prod

# 部署API服务
java -jar alert-converge-api/target/alert-converge-api.jar --spring.profiles.active=prod

# 部署任务调度服务
java -jar alert-converge-job/target/alert-converge-job.jar --spring.profiles.active=prod
```

## 监控和运维

### 健康检查
- API健康检查: `GET /alert-converge-api/actuator/health`
- 任务服务健康检查: `GET /alert-converge-job/actuator/health`

### 监控指标
- Prometheus指标: `/actuator/prometheus`
- 应用指标: `/actuator/metrics`

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一使用@ApiResponse注解包装响应
- 使用@PrintLog注解记录关键操作日志

### 数据库规范
- 表名使用t_前缀
- 字段名使用下划线命名
- 必须包含create_time、update_time、creator、updater、deleted字段
- 使用逻辑删除，不进行物理删除

### API设计规范
- 使用RESTful风格
- 统一使用@Tag和@Operation注解
- 请求参数使用BO对象
- 响应数据使用VO对象

## 常见问题

### Q: 如何添加新的预警模型？
A: 通过预警模型管理接口创建新模型，设置模型编码、名称、业务类型等信息。

### Q: 预警事件如何去重？
A: 系统基于关键字段生成MD5值，相同MD5的事件会被认为是重复事件。

### Q: 如何扩展新的数据源？
A: 在数据源管理中添加新的系统信息，并在代码中实现对应的数据源处理逻辑。

## 联系方式

- 项目负责人: 卢晓文
- 技术支持: 工具开发组
- 文档更新: 2025-01-04

## 许可证

本项目采用企业内部许可证，仅供内部使用。
