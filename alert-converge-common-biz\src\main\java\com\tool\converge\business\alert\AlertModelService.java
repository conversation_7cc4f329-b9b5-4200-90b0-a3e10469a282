package com.tool.converge.business.alert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryExtendSaveBo;
import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import com.tool.converge.repository.domain.alert.bo.AlertModelSaveBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelUpdateBO;
import com.tool.converge.repository.domain.alert.bo.AlertModelQueryParamsBO;
import com.tool.converge.repository.domain.alert.vo.AlertModelDetailVO;
import com.tool.converge.repository.domain.alert.vo.AlertModelPageVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 预警模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10 09:19:01
 */
public interface AlertModelService extends IService<AlertModelDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(AlertModelQueryExtendSaveBo saveBO);


    /**
     * 获取所有的预警名称
     *
     * @return
     */
    Set<String> getAllModelName();

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(AlertModelUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    AlertModelDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<AlertModelPageVO> getPageInfo(AlertModelQueryParamsBO queryParamsBO);

}
