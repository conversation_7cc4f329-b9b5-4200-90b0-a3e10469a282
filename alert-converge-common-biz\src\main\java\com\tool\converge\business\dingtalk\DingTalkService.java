package com.tool.converge.business.dingtalk;


import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.hzed.structure.common.exception.ServiceException;
import com.tool.converge.business.dingtalk.config.DingTalkConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 钉钉服务类
 * @date 2025/8/4 17:41
 */
@Service
@Slf4j
public class DingTalkService {

    @Resource
    private DingTalkConfig dingTalkConfig;

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    /**
     * 钉钉access_token缓存key前缀
     */
    public static final String CACHE_KEY_PREFIX = "dingtalk:accessToken:";
    /**
     * 钉钉access_token缓存时间
     */
    public static final Long CACHE_TIME = 3600L;

    /**
     * 获取access_token
     *
     * @return
     */
    public String getAccessToken() {
        try {
            String cacheKey = CACHE_KEY_PREFIX + dingTalkConfig.getAppKey();
            String token = redisTemplate.opsForValue().get(cacheKey);
            if (token == null) {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
                OapiGettokenRequest request = new OapiGettokenRequest();
                request.setAppkey(dingTalkConfig.getAppKey());
                request.setAppsecret(dingTalkConfig.getAppSecret());
                request.setHttpMethod("GET");
                OapiGettokenResponse response = client.execute(request);
                if (!response.isSuccess()) {
                    throw new RuntimeException("获取Token失败: " + response.getErrmsg());
                }
                token = response.getAccessToken();
                redisTemplate.opsForValue().set(cacheKey, token, CACHE_TIME, TimeUnit.SECONDS);
            }
            return token;
        } catch (Exception e) {
            log.error("获取access_token异常", e);
            throw new ServiceException("获取access_token异常");
        }
    }

    /**
     * 获取部门列表
     *
     * @return
     * @throws Exception
     */
    public List<OapiV2DepartmentListsubResponse.DeptBaseResponse> getDepartmentList() {

        String token = getAccessToken();
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
            req.setLanguage("zh_CN");
            OapiV2DepartmentListsubResponse rsp = client.execute(req, token);
            if (!rsp.isSuccess()) {
                throw new RuntimeException("获取部门失败: " + rsp.getErrmsg());
            }
            return rsp.getResult();
        } catch (Exception e) {
            log.error("获取部门列表异常", e);
            throw new ServiceException("获取部门列表异常");
        }
    }

    /**
     * 获取所有通讯录成员
     *
     * @return
     */
    public List<OapiV2UserListResponse.ListUserResponse> getUserList(Long deptId) {
        String token = getAccessToken();
        List<OapiV2UserListResponse.ListUserResponse> allUsers = new ArrayList<>();
        try {
            long cursor = 0L;
            boolean hasMore;
            do {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
                OapiV2UserListRequest req = new OapiV2UserListRequest();
                req.setDeptId(deptId);
                req.setCursor(cursor);
                req.setSize(100L);
                OapiV2UserListResponse res = client.execute(req, token);
                if (!res.isSuccess()) {
                    throw new RuntimeException("获取成员失败: " + res.getErrmsg());
                }
                allUsers.addAll(res.getResult().getList());
                hasMore = res.getResult().getHasMore();
                cursor = res.getResult().getNextCursor();
            } while (hasMore);
            return allUsers;
        } catch (Exception e) {
            log.error("获取所有通讯录成员异常", e);
            throw new ServiceException("获取所有通讯录成员异常");
        }
    }

}